import os

#
import httpx

#
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# https://github.com/alan-turing-institute/ReadabiliPy
import readabilipy.simple_json
from readabilipy import simple_tree_from_html_string

# https://github.com/matthewwithanm/python-markdownify
import markdownify

# For HTML preprocessing
from bs4 import BeautifulSoup


HTML2MD_LOGO = """
██╗  ██╗████████╗███╗   ███╗██╗     ██████╗ ███╗   ███╗██████╗
██║  ██║╚══██╔══╝████╗ ████║██║     ╚════██╗████╗ ████║██╔══██╗
███████║   ██║   ██╔████╔██║██║      █████╔╝██╔████╔██║██║  ██║
██╔══██║   ██║   ██║╚██╔╝██║██║     ██╔═══╝ ██║╚██╔╝██║██║  ██║
██║  ██║   ██║   ██║ ╚═╝ ██║███████╗███████╗██║ ╚═╝ ██║██████╔╝
╚═╝  ╚═╝   ╚═╝   ╚═╝     ╚═╝╚══════╝╚══════╝╚═╝     ╚═╝╚═════╝
"""

console = Console()


def fetch_url(url: str) -> str:
    """
    Fetch content from a URL using httpx.

    Args:
        url (str): The URL to fetch
        console (Console): Rich console instance for output

    Returns:
        str: The response content as text
    """
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task(f"Récupération de {url}...", total=None)

            with httpx.Client() as client:
                response = client.get(
                    url,
                    headers={"User-Agent": "Mozilla/5.0"},
                    timeout=30.0,
                    follow_redirects=True,
                )
                response.raise_for_status()
                progress.update(task, completed=True)

        console.print(f"✅ Contenu récupéré avec succès depuis {url}", style="green")

        page_raw = response.text
        content_type = response.headers.get("content-type", "")

        is_page_html = (
            "<html" in page_raw[:100] or "text/html" in content_type or not content_type
        )

        if is_page_html:
            return extract_content_from_html(page_raw)

        console.print(f"❌ Aucun HTML trouvé pour {url}", style="red")
        return ""
    except httpx.RequestError as e:
        console.print(
            f"❌ Erreur réseau lors de la requête vers {url}: {e}", style="red"
        )
        return ""
    except httpx.HTTPStatusError as e:
        console.print(
            f"❌ Erreur HTTP {e.response.status_code} lors de la requête vers {url}",
            style="red",
        )
        return ""


def elements_to_delete():
    """Elements that will be deleted together with their contents."""
    html5_form_elements = [
        "button",
        "datalist",
        "fieldset",
        "form",
        "input",
        "label",
        "legend",
        "meter",
        "optgroup",
        "option",
        "output",
        "progress",
        "select",
        "textarea",
    ]
    html5_image_elements = ["area", "img", "map", "picture", "source"]
    html5_media_elements = ["audio", "track", "video"]
    html5_embedded_elements = ["embed", "iframe", "math", "object", "param", "svg"]
    html5_interactive_elements = ["details", "dialog", "summary"]
    html5_scripting_elements = ["canvas", "noscript", "script", "template"]
    html5_data_elements = ["data", "link"]
    html5_formatting_elements = ["style"]
    html5_navigation_elements = ["nav"]

    elements = (
        html5_form_elements
        + html5_image_elements
        + html5_media_elements
        + html5_embedded_elements
        + html5_interactive_elements
        + html5_scripting_elements
        + html5_data_elements
        + html5_formatting_elements
        + html5_navigation_elements
    )

    return elements


def preprocess_html(html: str) -> str:
    """Preprocess HTML to remove unwanted elements completely.

    Args:
        html: Raw HTML content to process

    Returns:
        Cleaned HTML with unwanted elements removed
    """

    html5_scripting_elements = ["canvas", "noscript", "script", "template"]
    html5_navigation_elements = ["nav"]
    illegal_elements = ["form", "head"]

    elements_to_remove = (
        html5_scripting_elements + html5_navigation_elements + illegal_elements
    )

    soup = BeautifulSoup(html, "html.parser")

    for element_name in elements_to_remove:
        for element in soup.find_all(element_name):
            element.decompose()

    return str(soup)


def extract_content_from_html(html: str) -> str:
    """Extract and convert HTML content to Markdown format.

    Args:
        html: Raw HTML content to process

    Returns:
        Simplified markdown version of the content
    """

    cleaned_html = preprocess_html(html)

    """
    ret = readabilipy.simple_json.simple_json_from_html_string(
        cleaned_html, use_readability=True
    )

    if not ret["content"]:
        console.print("❌ Échec de la simplification de la page HTML", style="bold red")
        return ""
    """
    content = markdownify.markdownify(
        cleaned_html,  # ret["content"],
        heading_style=markdownify.ATX,
        autolinks=True,
        default_title=True,
    )

    return content


def main():
    try:
        url = "https://zonetuto.fr/"
        console.print(f"\n🌐 URL cible: [cyan]{url}[/cyan]")

        name_without_suff = "Test"
        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_md_dir = os.path.join(script_dir, "output", name_without_suff)

        console.print(
            Panel.fit(
                f"[bold cyan]{HTML2MD_LOGO}[/bold cyan]\n"
                "[bold blue]         📝 Conversion HTML vers Markdown[/bold blue]",
                border_style="blue",
            )
        )

        console.print(f"📁 [cyan]Répertoire de sortie:[/cyan] {local_md_dir}")
        os.makedirs(local_md_dir, exist_ok=True)

        md_content = fetch_url(url)
        if md_content:
            output_md_path = os.path.join(
                local_md_dir, f"html2md_{name_without_suff}.md"
            )
            with open(output_md_path, "w", encoding="utf-8") as f:
                f.write(md_content)

            console.print(
                f"💾 [bold green]Résultat sauvegardé:[/bold green] [link]{output_md_path}[/link]"
            )
        else:
            console.print("❌ Échec de la récupération du contenu", style="bold red")
    except KeyboardInterrupt:
        return


if __name__ == "__main__":
    main()
